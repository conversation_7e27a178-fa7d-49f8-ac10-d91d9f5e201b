<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lookahead Trainer</title>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f0f0f0;
    }

    canvas {
      display: block;
    }

    .control-panel {
      position: absolute;
      top: 15px;
      left: 15px;
      background: rgba(255, 255, 255, 0.95);
      padding: 15px;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10;
      min-width: 280px;
    }

    .timer-display {
      position: absolute;
      top: 15px;
      right: 15px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 8px;
      font-size: 24px;
      font-weight: bold;
      font-family: 'Courier New', monospace;
      z-index: 10;
      min-width: 120px;
      text-align: center;
    }

    .control-section {
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .control-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .button-group {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-top: 8px;
    }

    button {
      padding: 8px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-success:hover {
      background: #1e7e34;
    }

    .btn-warning {
      background: #ffc107;
      color: #212529;
    }

    .btn-warning:hover {
      background: #e0a800;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 5px;
    }

    input[type="number"] {
      width: 60px;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    input[type="checkbox"] {
      margin-right: 8px;
    }

    .status-display {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 6px;
      border-left: 4px solid #007bff;
      font-size: 14px;
      line-height: 1.4;
    }

    .scramble-display {
      background: #fff3cd;
      padding: 10px;
      border-radius: 6px;
      border-left: 4px solid #ffc107;
      font-family: 'Courier New', monospace;
      font-size: 16px;
      font-weight: bold;
      word-break: break-all;
    }

    .hidden {
      display: none;
    }

    label {
      font-size: 14px;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <!-- Timer Display -->
  <div class="timer-display" id="timerDisplay">00:00.0</div>

  <!-- Control Panel -->
  <div class="control-panel">
    <!-- Settings Section -->
    <div class="control-section">
      <label>
        <input type="checkbox" id="toggleColors" checked> Show colors
      </label>
      <div class="input-group">
        <label for="scrambleLength">Scramble length:</label>
        <input type="number" id="scrambleLength" value="4" min="1" max="20">
      </div>
    </div>

    <!-- Game Controls -->
    <div class="control-section">
      <div class="button-group">
        <button class="btn-primary" onclick="trainer.selectRandomPiece()">Random Start</button>
        <button class="btn-success" onclick="trainer.startRound()" id="startButton" disabled>Start Round</button>
        <button class="btn-warning" onclick="trainer.revealAnswer()" id="revealButton" disabled>Reveal Answer</button>
        <button class="btn-danger" onclick="trainer.reset()">Reset</button>
      </div>
    </div>

    <!-- Status Display -->
    <div class="control-section">
      <div class="status-display" id="status">Click a piece/sticker to select it, or use "Random Start"</div>
    </div>

    <!-- Scramble Display -->
    <div class="control-section hidden" id="scrambleSection">
      <div class="scramble-display" id="scrambleDisplay"></div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script type="importmap">
  {
    "imports": {
      "three": "./three/build/three.module.js",
      "three/addons/": "./three/examples/jsm/"
    }
  }
  </script>

  <script type="module">
    import * as THREE from 'three';
    import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
    import { FontLoader } from 'three/addons/loaders/FontLoader.js';
    import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

    // ======================
    // CONSTANTS & CONFIG
    // ======================

    const CONFIG = {
      CUBE_SPACING: 1.05,
      STICKER_OFFSET: 0.501,
      STICKER_SIZE: 0.9,
      ANIMATION_STEPS: 15,
      PULSE_SPEED: 0.005
    };

    const FACE_COLORS = {
      X: { "1": 0xff0000, "-1": 0xff8000 },  // Red/Orange
      Y: { "1": 0xffffff, "-1": 0xffff00 },  // White/Yellow
      Z: { "1": 0x00ff00, "-1": 0x0000ff }   // Green/Blue
    };

    const MOVE_MAPPING = {
      U: ['Y', 1], D: ['Y', -1],
      R: ['X', 1], L: ['X', -1],
      F: ['Z', 1], B: ['Z', -1]
    };

    const FACE_LABELS = {
      U: { axis: 'Y', value: 1, label: 'U' },
      D: { axis: 'Y', value: -1, label: 'D' },
      F: { axis: 'Z', value: 1, label: 'F' },
      B: { axis: 'Z', value: -1, label: 'B' },
      L: { axis: 'X', value: -1, label: 'L' },
      R: { axis: 'X', value: 1, label: 'R' }
    };

    // ======================
    // SCENE SETUP
    // ======================

    class CubeRenderer {
      constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.controls = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        this.init();
      }

      init() {
        // Camera setup
        this.camera.position.set(2.6, 2.6, 6.4);
        this.camera.lookAt(this.scene.position);

        // Renderer setup
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(this.renderer.domElement);

        // Controls setup
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.1;
        this.controls.rotateSpeed = 0.5;
        this.controls.zoomSpeed = 0.5;

        // Lighting setup
        this.setupLighting();

        // Event listeners
        this.setupEventListeners();
      }

      setupLighting() {
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 7.5);
        this.scene.add(directionalLight);

        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        this.scene.add(ambientLight);
      }

      setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('click', (event) => this.onClick(event));
      }

      onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
      }

      onClick(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);

        if (intersects.length > 0) {
          cube.handleCubeletClick(intersects[0]);
        }
      }

      render() {
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
      }
    }

    // ======================
    // CUBE LOGIC
    // ======================

    class RubiksCube {
      constructor(renderer) {
        this.renderer = renderer;
        this.cubelets = [];
        this.selectedCubelet = null;
        this.trackedCubelet = null;
        this.animating = false;

        this.createCube();
        this.addFaceLabels();
      }

      createCube() {
        // Create all 26 pieces (excluding center)
        for (let x = -1; x <= 1; x++) {
          for (let y = -1; y <= 1; y++) {
            for (let z = -1; z <= 1; z++) {
              if (x === 0 && y === 0 && z === 0) continue; // Skip center

              this.createCubelet(x, y, z);
            }
          }
        }
      }

      createCubelet(x, y, z) {
        // Create base cubelet
        const cubelet = new THREE.Mesh(
          new THREE.BoxGeometry(1, 1, 1),
          new THREE.MeshLambertMaterial({ color: 0x222222 })
        );

        cubelet.position.set(x * CONFIG.CUBE_SPACING, y * CONFIG.CUBE_SPACING, z * CONFIG.CUBE_SPACING);
        cubelet.userData = {
          position: [x, y, z],
          orientation: 0
        };

        // Add stickers to visible faces
        if (x !== 0) this.addSticker(cubelet, 'X', x);
        if (y !== 0) this.addSticker(cubelet, 'Y', y);
        if (z !== 0) this.addSticker(cubelet, 'Z', z);

        this.renderer.scene.add(cubelet);
        this.cubelets.push(cubelet);
      }

      addSticker(parent, axis, direction) {
        const showColors = document.getElementById('toggleColors')?.checked ?? true;
        const color = showColors ? FACE_COLORS[axis][direction.toString()] : 0xcccccc;

        const material = new THREE.MeshBasicMaterial({
          color,
          side: THREE.DoubleSide,
          depthTest: true,
          depthWrite: true
        });
        material.userData = { baseColor: color };

        const geometry = new THREE.PlaneGeometry(CONFIG.STICKER_SIZE, CONFIG.STICKER_SIZE);
        const sticker = new THREE.Mesh(geometry, material);

        // Position sticker on correct face
        this.positionSticker(sticker, axis, direction);
        parent.add(sticker);
      }

      positionSticker(sticker, axis, direction) {
        const offset = CONFIG.STICKER_OFFSET;

        switch (axis) {
          case 'X':
            sticker.rotation.y = Math.PI / 2;
            sticker.position.x = direction * offset;
            break;
          case 'Y':
            sticker.rotation.x = -Math.PI / 2;
            sticker.position.y = direction * offset;
            break;
          case 'Z':
            sticker.position.z = direction * offset;
            break;
        }
      }

      addFaceLabels() {
        for (const key in FACE_LABELS) {
          const { axis, value, label } = FACE_LABELS[key];
          const sprite = this.createFaceLabel(label);

          sprite.position[axis.toLowerCase()] = value * 1.75;

          // Try to attach to center piece, otherwise add to scene
          const center = this.cubelets.find(c =>
            c.userData.position[this.axisToIndex(axis)] === value &&
            c.userData.position.filter(v => v === 0).length === 2
          );

          if (center) {
            center.add(sprite);
          } else {
            this.renderer.scene.add(sprite);
          }
        }
      }

      createFaceLabel(text) {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 128;
        const ctx = canvas.getContext('2d');

        ctx.font = 'bold 64px sans-serif';
        ctx.fillStyle = 'black';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 4;
        ctx.strokeText(text, 64, 64);
        ctx.fillText(text, 64, 64);

        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;

        const material = new THREE.SpriteMaterial({
          map: texture,
          depthTest: true,
          depthWrite: false
        });

        const sprite = new THREE.Sprite(material);
        sprite.renderOrder = 999;
        sprite.scale.set(0.9, 0.9, 1);

        return sprite;
      }

      // ======================
      // SELECTION & INTERACTION
      // ======================

      handleCubeletClick(intersect) {
        const intersected = intersect.object;
        const parentCubelet = intersected.parent.type === 'Mesh' ? intersected.parent : intersected;

        // Skip invalid selections
        if (!this.isValidCubelet(parentCubelet)) return;

        // Handle training mode clicks - check for sticker clicks during guessing
        if (trainer.awaitingUserGuess && this.selectedCubelet) {
          // User is making a guess - check if they clicked a sticker
          if (intersected.type === 'Mesh' && intersected.geometry.type === 'PlaneGeometry') {
            trainer.handleStickerGuess(parentCubelet, intersected);
            return;
          }
        }

        // Update selection (only when not in guessing mode)
        if (!trainer.awaitingUserGuess) {
          this.updateSelection(parentCubelet, intersected);
          // Notify trainer that a piece was selected
          trainer.onPieceSelected();
        }
      }

      isValidCubelet(cubelet) {
        if (!cubelet.userData || !cubelet.userData.position) return false;

        // Skip center pieces (only 1 axis non-zero)
        const nonZeroAxes = cubelet.userData.position.filter(v => v !== 0);
        return nonZeroAxes.length > 1;
      }

      updateSelection(cubelet, sticker) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.selectedCubelet = cubelet;
        this.trackedCubelet = cubelet;

        // Store both sticker reference and which face it represents
        sticker.userData.originalColor = sticker.material.userData?.baseColor ?? sticker.material.color.getHex();
        cubelet.userData.originalSticker = sticker;

        // Store which face this sticker is on for tracking
        cubelet.userData.selectedStickerFace = this.getStickerFaceInfo(sticker);
      }

      getStickerFaceInfo(sticker) {
        // Determine which face this sticker is on
        const axis = this.getStickerAxis(sticker);
        const direction = Math.sign(sticker.position[axis.toLowerCase()]);
        return { axis, direction };
      }

      clearSelection() {
        if (!this.selectedCubelet) return;

        // Reset cubelet appearance
        this.selectedCubelet.material.color.setHex(0x222222);
        this.selectedCubelet.material.emissive.setHex(0x000000);
        this.selectedCubelet.material.emissiveIntensity = 0;
        this.selectedCubelet.material.needsUpdate = true;

        // Reset sticker appearance
        const sticker = this.selectedCubelet.userData.originalSticker;
        if (sticker && sticker.material && sticker.userData.originalColor !== undefined) {
          sticker.material.color.setHex(sticker.userData.originalColor);
        }

        // Clean up references
        if (this.selectedCubelet.userData) {
          this.selectedCubelet.userData.originalSticker = null;
        }
      }

      // ======================
      // CUBE MOVES & ANIMATION
      // ======================

      applyMoves(moves) {
        if (this.animating) return;

        const sequence = typeof moves === 'string' ? moves.split(' ') : moves;
        let index = 0;

        const executeNextMove = () => {
          if (index >= sequence.length) {
            this.animating = false;
            this.highlightTrackedPiece();
            return;
          }

          const move = sequence[index++];
          this.executeMove(move, executeNextMove);
        };

        this.animating = true;
        executeNextMove();
      }

      executeMove(move, callback) {
        const times = move.endsWith('2') ? 2 : 1;
        const baseMove = move.replace('2', '');
        let count = 0;

        const repeatMove = () => {
          if (count++ < times) {
            this.rotateLayer(baseMove, repeatMove);
          } else {
            callback();
          }
        };

        repeatMove();
      }

      rotateLayer(move, callback) {
        const base = move[0];
        const prime = move.includes("'");
        const [axis, layerIndex] = MOVE_MAPPING[base];

        // Get cubelets in this layer
        const threshold = 0.01;
        const layer = this.cubelets.filter(c =>
          Math.abs(c.userData.position[this.axisToIndex(axis)] - layerIndex) < threshold
        );

        // Create rotation group
        const group = new THREE.Group();
        layer.forEach(c => group.add(c));
        this.renderer.scene.add(group);

        // Calculate rotation
        const invertedFaces = ['D', 'L', 'B'];
        const isInverted = invertedFaces.includes(base);
        const angle = (Math.PI / 2) * (prime ? 1 : -1) * (isInverted ? -1 : 1);
        const rotAxis = new THREE.Vector3(
          axis === 'X' ? 1 : 0,
          axis === 'Y' ? 1 : 0,
          axis === 'Z' ? 1 : 0
        );

        this.animateRotation(group, layer, rotAxis, angle, callback);
      }

      animateRotation(group, layer, rotAxis, totalAngle, callback) {
        let currentStep = 0;
        const stepAngle = totalAngle / CONFIG.ANIMATION_STEPS;

        const animateStep = () => {
          if (currentStep++ < CONFIG.ANIMATION_STEPS) {
            group.rotateOnAxis(rotAxis, stepAngle);
            requestAnimationFrame(animateStep);
          } else {
            this.finishRotation(group, layer);
            if (callback) callback();
          }
        };

        animateStep();
      }

      finishRotation(group, layer) {
        group.updateMatrixWorld(true);

        layer.forEach(cubelet => {
          // Update orientation for edge pieces
          this.updateCubeletOrientation(cubelet, group);

          // Apply transformation and update position
          cubelet.applyMatrix4(group.matrixWorld);
          cubelet.matrixAutoUpdate = true;
          this.renderer.scene.add(cubelet);

          // Update logical position
          cubelet.userData.position = [
            Math.round(cubelet.position.x / CONFIG.CUBE_SPACING),
            Math.round(cubelet.position.y / CONFIG.CUBE_SPACING),
            Math.round(cubelet.position.z / CONFIG.CUBE_SPACING)
          ];
        });

        this.renderer.scene.remove(group);
      }

      updateCubeletOrientation(cubelet, group) {
        // Determine if this is an edge piece and should flip orientation
        const pos = cubelet.userData.position;
        const nonZeroAxes = pos.map((v, i) => v !== 0 ? i : -1).filter(i => i !== -1);
        const isEdge = nonZeroAxes.length === 2;

        // Get rotation axis from group's rotation
        // This is a simplified version - in practice you'd track the rotation axis
        // For now, we'll use a heuristic based on the move being performed
        if (isEdge) {
          // Flip orientation for edge pieces in certain rotations
          // This is a simplified implementation
          cubelet.userData.orientation ^= 1;
        }
      }

      highlightTrackedPiece() {
        if (this.selectedCubelet) {
          this.selectedCubelet.material.color.set(0x00ff00);
        }
      }

      // ======================
      // COLOR MANAGEMENT
      // ======================

      toggleColors() {
        const showColors = document.getElementById('toggleColors').checked;

        this.cubelets.forEach(cubelet => {
          cubelet.children.forEach(sticker => {
            if (!sticker.material) return;

            const axis = this.getStickerAxis(sticker);
            const direction = Math.sign(sticker.position[axis.toLowerCase()]);
            const color = showColors ? FACE_COLORS[axis][direction.toString()] : 0xcccccc;

            this.animateColorTransition(sticker, color);
          });
        });
      }

      getStickerAxis(sticker) {
        if (Math.abs(sticker.position.x) > 0.5) return 'X';
        if (Math.abs(sticker.position.y) > 0.5) return 'Y';
        return 'Z';
      }

      animateColorTransition(sticker, targetColor) {
        const currentColor = sticker.material.color.clone();
        const newColor = new THREE.Color(targetColor);
        let progress = 0;
        const duration = 300;
        const steps = 30;
        const stepTime = duration / steps;

        const animate = () => {
          progress += 1 / steps;
          sticker.material.color.lerpColors(currentColor, newColor, progress);
          if (progress < 1) {
            setTimeout(animate, stepTime);
          }
        };

        animate();
      }

      // ======================
      // UTILITY METHODS
      // ======================

      axisToIndex(axis) {
        return { X: 0, Y: 1, Z: 2 }[axis];
      }

      snapToAxis(value) {
        const epsilon = 0.1;
        if (Math.abs(value - CONFIG.CUBE_SPACING) < epsilon) return 1;
        if (Math.abs(value + CONFIG.CUBE_SPACING) < epsilon) return -1;
        if (Math.abs(value) < epsilon) return 0;
        return NaN;
      }

      reset(clearSelection = true) {
        if (clearSelection) {
          this.clearSelection();
          this.selectedCubelet = null;
          this.trackedCubelet = null;
        }

        // Reset all cubelets to default state
        this.cubelets.forEach(c => {
          c.material.color.set(0x222222);
          c.material.emissive.setHex(0x000000);
          c.material.emissiveIntensity = 0;
          c.userData.orientation = 0;

          // Clean up any tracking flags
          c.userData._isTracked = false;

          // Reset sticker colors and clean up tracking
          c.children.forEach(sticker => {
            if (sticker.userData) {
              sticker.userData._isTrackedSticker = false;
            }
          });
        });

        // Reset cube to solved state by recreating it
        // This is more reliable than trying to track all moves
        this.resetToSolvedState();
      }

      resetToSolvedState() {
        // Remove all existing cubelets
        this.cubelets.forEach(cubelet => {
          this.renderer.scene.remove(cubelet);
        });
        this.cubelets = [];

        // Recreate the cube in solved state
        this.createCube();
        this.addFaceLabels();
      }

      // Animation update for selected piece pulsing
      updateAnimation() {
        if (this.selectedCubelet && this.selectedCubelet.userData.originalSticker) {
          const t = Date.now() * CONFIG.PULSE_SPEED;
          const blend = 0.5 + 0.5 * Math.sin(t);

          const sticker = this.selectedCubelet.userData.originalSticker;
          const baseColor = new THREE.Color(sticker.userData.originalColor);
          const targetColor = new THREE.Color(0xba55d3); // Purple highlight
          sticker.material.color.copy(baseColor).lerp(targetColor, blend);
        }

        if (this.selectedCubelet) {
          const t = Date.now() * CONFIG.PULSE_SPEED;
          const intensity = 0.5 + 0.25 * Math.sin(t);
          this.selectedCubelet.material.emissive = new THREE.Color(0xba55d3);
          this.selectedCubelet.material.emissiveIntensity = intensity;
          this.selectedCubelet.material.needsUpdate = true;
        }
      }
    }

    // ======================
    // TIMER SYSTEM
    // ======================

    class GameTimer {
      constructor() {
        this.startTime = null;
        this.isRunning = false;
        this.display = document.getElementById('timerDisplay');
        this.updateInterval = null;
      }

      start() {
        this.startTime = Date.now();
        this.isRunning = true;
        this.updateInterval = setInterval(() => this.updateDisplay(), 100);
      }

      stop() {
        this.isRunning = false;
        if (this.updateInterval) {
          clearInterval(this.updateInterval);
          this.updateInterval = null;
        }
        return this.getElapsedTime();
      }

      reset() {
        this.stop();
        this.startTime = null;
        this.display.textContent = '00:00.0';
      }

      getElapsedTime() {
        if (!this.startTime) return 0;
        return (Date.now() - this.startTime) / 1000;
      }

      updateDisplay() {
        if (!this.isRunning || !this.startTime) return;

        const elapsed = this.getElapsedTime();
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;

        this.display.textContent =
          `${minutes.toString().padStart(2, '0')}:${seconds.toFixed(1).padStart(4, '0')}`;
      }
    }

    // ======================
    // TRAINING SYSTEM
    // ======================

    class LookaheadTrainer {
      constructor(cube) {
        this.cube = cube;
        this.timer = new GameTimer();
        this.gameState = 'IDLE'; // IDLE, PIECE_SELECTED, SCRAMBLE_SHOWN, AWAITING_GUESS, EVALUATING
        this.currentScramble = [];
        this.originalCubelet = null;
        this.originalSticker = null;
        this.userGuess = null;
        this.correctAnswer = null;

        this.updateUI();
      }

      // ======================
      // GAME STATE MANAGEMENT
      // ======================

      updateUI() {
        const startButton = document.getElementById('startButton');
        const revealButton = document.getElementById('revealButton');
        const scrambleSection = document.getElementById('scrambleSection');

        switch (this.gameState) {
          case 'IDLE':
            startButton.disabled = true;
            revealButton.disabled = true;
            scrambleSection.classList.add('hidden');
            break;
          case 'PIECE_SELECTED':
            startButton.disabled = false;
            revealButton.disabled = true;
            scrambleSection.classList.add('hidden');
            break;
          case 'SCRAMBLE_SHOWN':
            startButton.disabled = true;
            revealButton.disabled = true;
            scrambleSection.classList.remove('hidden');
            break;
          case 'AWAITING_GUESS':
            startButton.disabled = true;
            revealButton.disabled = false;
            scrambleSection.classList.remove('hidden');
            break;
          case 'EVALUATING':
            startButton.disabled = true;
            revealButton.disabled = true;
            scrambleSection.classList.remove('hidden');
            break;
        }
      }

      selectRandomPiece() {
        // Get all valid cubelets (edges and corners)
        const validCubelets = this.cube.cubelets.filter(cubelet => {
          const nonZeroAxes = cubelet.userData.position.filter(v => v !== 0);
          return nonZeroAxes.length > 1; // Edge or corner pieces
        });

        if (validCubelets.length === 0) return;

        // Select random cubelet
        const randomCubelet = validCubelets[Math.floor(Math.random() * validCubelets.length)];

        // Get all stickers on this cubelet
        const stickers = randomCubelet.children.filter(child =>
          child.type === 'Mesh' && child.geometry.type === 'PlaneGeometry'
        );

        if (stickers.length === 0) return;

        // Select random sticker
        const randomSticker = stickers[Math.floor(Math.random() * stickers.length)];

        // Update selection
        this.cube.updateSelection(randomCubelet, randomSticker);
        this.gameState = 'PIECE_SELECTED';
        this.updateUI();
        this.updateStatus('Random piece selected! Click "Start Round" to begin.');
      }

      startRound() {
        if (this.gameState !== 'PIECE_SELECTED') {
          this.updateStatus('Please select a piece first!');
          return;
        }

        // Store references to the original cubelet and sticker
        if (this.cube.selectedCubelet && this.cube.selectedCubelet.userData.originalSticker) {
          this.originalCubelet = this.cube.selectedCubelet;
          this.originalSticker = this.cube.selectedCubelet.userData.originalSticker;
        } else {
          this.updateStatus('Please select a piece first!');
          return;
        }

        const length = parseInt(document.getElementById('scrambleLength').value);
        this.currentScramble = this.generateScramble(length);

        document.getElementById('scrambleDisplay').textContent = this.currentScramble.join(' ');
        this.gameState = 'SCRAMBLE_SHOWN';
        this.updateUI();
        this.updateStatus('Study the scramble, then click anywhere to start the timer and make your prediction.');

        // Add click listener to start prediction phase
        this.addStartPredictionListener();
      }

      addStartPredictionListener() {
        const clickHandler = () => {
          document.removeEventListener('click', clickHandler);
          this.startPredictionPhase();
        };
        document.addEventListener('click', clickHandler);
      }

      startPredictionPhase() {
        this.timer.start();
        this.gameState = 'AWAITING_GUESS';
        this.updateUI();
        this.updateStatus('Timer started! Click on the sticker where you think the selected piece will end up.');
      }

      generateScramble(length = 10) {
        const faces = ['U', 'D', 'R', 'L', 'F', 'B'];
        const modifiers = ['', "'", '2'];
        const scramble = [];

        while (scramble.length < length) {
          const face = faces[Math.floor(Math.random() * faces.length)];
          const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
          const move = face + modifier;

          // Avoid immediate cancellation or repetition
          const lastMove = scramble[scramble.length - 1];
          if (!lastMove || !this.movesConflict(lastMove, move)) {
            scramble.push(move);
          }
        }

        return scramble;
      }

      movesConflict(move1, move2) {
        // Simple conflict detection - same face
        return move1[0] === move2[0];
      }

      // ======================
      // GUESS HANDLING
      // ======================

      handleStickerGuess(guessedCubelet, guessedSticker) {
        if (this.gameState !== 'AWAITING_GUESS') return;

        const elapsedTime = this.timer.stop();

        // Store the user's guess
        this.userGuess = {
          cubelet: guessedCubelet,
          sticker: guessedSticker,
          time: elapsedTime
        };

        this.gameState = 'EVALUATING';
        this.updateUI();
        this.updateStatus('Evaluating your guess...');

        // Calculate the correct answer and compare
        this.evaluateGuess();
      }

      evaluateGuess() {
        // Mark the original pieces for tracking
        this.originalCubelet.userData._isTracked = true;
        this.originalSticker.userData._isTrackedSticker = true;

        // Apply the scramble to find the correct answer
        this.cube.applyMoves(this.currentScramble);

        // Wait for animation to complete
        const checkComplete = setInterval(() => {
          if (!this.cube.animating) {
            clearInterval(checkComplete);
            this.compareGuessWithAnswer();
          }
        }, 50);
      }

      compareGuessWithAnswer() {
        // Find the tracked cubelet and sticker after scramble
        const correctCubelet = this.cube.cubelets.find(c => c.userData._isTracked);
        let correctSticker = null;

        if (correctCubelet) {
          correctSticker = correctCubelet.children.find(c => c.userData._isTrackedSticker);
        }

        // Clean up tracking flags
        this.originalCubelet.userData._isTracked = false;
        this.originalSticker.userData._isTrackedSticker = false;

        // Store correct answer
        this.correctAnswer = {
          cubelet: correctCubelet,
          sticker: correctSticker
        };

        // Evaluate the guess
        const locationCorrect = this.userGuess.cubelet === correctCubelet;
        const stickerCorrect = this.userGuess.sticker === correctSticker;

        this.showResults(locationCorrect, stickerCorrect);
      }

      showResults(locationCorrect, stickerCorrect) {
        const time = this.userGuess.time.toFixed(2);
        let message, isSuccess = false;

        if (locationCorrect && stickerCorrect) {
          message = `🎉 Perfect! Correct in ${time}s`;
          isSuccess = true;
        } else if (locationCorrect && !stickerCorrect) {
          message = `🟡 Right piece, wrong sticker face. Time: ${time}s`;
        } else {
          message = `❌ Incorrect. Time: ${time}s`;
        }

        // Highlight the correct answer
        if (this.correctAnswer.sticker) {
          this.highlightCorrectAnswer(this.correctAnswer.sticker);
        }

        this.updateStatus(message);

        // Auto-reset after a delay or wait for manual reset
        if (isSuccess) {
          setTimeout(() => {
            this.reset();
          }, 3000);
        }
      }

      revealAnswer() {
        if (this.gameState !== 'AWAITING_GUESS') return;

        this.timer.stop();
        this.gameState = 'EVALUATING';
        this.updateUI();
        this.updateStatus('Revealing answer...');

        // Calculate and show the correct answer without user guess
        this.showAnswerOnly();
      }

      showAnswerOnly() {
        // Mark the original pieces for tracking
        this.originalCubelet.userData._isTracked = true;
        this.originalSticker.userData._isTrackedSticker = true;

        // Apply the scramble to find the correct answer
        this.cube.applyMoves(this.currentScramble);

        // Wait for animation to complete
        const checkComplete = setInterval(() => {
          if (!this.cube.animating) {
            clearInterval(checkComplete);
            this.displayAnswer();
          }
        }, 50);
      }

      displayAnswer() {
        // Find the tracked cubelet and sticker after scramble
        const correctCubelet = this.cube.cubelets.find(c => c.userData._isTracked);
        let correctSticker = null;

        if (correctCubelet) {
          correctSticker = correctCubelet.children.find(c => c.userData._isTrackedSticker);
        }

        // Clean up tracking flags
        this.originalCubelet.userData._isTracked = false;
        this.originalSticker.userData._isTrackedSticker = false;

        // Highlight the correct answer
        if (correctSticker) {
          this.highlightCorrectAnswer(correctSticker);
        }

        this.updateStatus('💡 Answer revealed! The selected sticker ends up here.');
      }

      // ======================
      // UTILITY METHODS
      // ======================

      highlightCorrectAnswer(sticker) {
        // Briefly highlight the correct sticker
        const originalColor = sticker.material.color.getHex();
        sticker.material.color.setHex(0x00ff00); // Green

        setTimeout(() => {
          sticker.material.color.setHex(originalColor);
        }, 3000);
      }

      updateStatus(message) {
        document.getElementById('status').textContent = message;
      }

      reset() {
        this.timer.reset();
        this.gameState = 'IDLE';
        this.currentScramble = [];
        this.originalCubelet = null;
        this.originalSticker = null;
        this.userGuess = null;
        this.correctAnswer = null;

        document.getElementById('scrambleDisplay').textContent = '';
        this.updateUI();
        this.updateStatus('Click a piece/sticker to select it, or use "Random Start"');
        this.cube.reset();
      }

      // ======================
      // CUBE INTERACTION BRIDGE
      // ======================

      onPieceSelected() {
        if (this.gameState === 'IDLE') {
          this.gameState = 'PIECE_SELECTED';
          this.updateUI();
          this.updateStatus('Piece selected! Click "Start Round" to begin.');
        }
      }

      // This method is called by the cube when a sticker is clicked during guessing
      get awaitingUserGuess() {
        return this.gameState === 'AWAITING_GUESS';
      }
    }

    // ======================
    // MAIN APPLICATION
    // ======================

    // Initialize the application
    const renderer = new CubeRenderer();
    const cube = new RubiksCube(renderer);
    const trainer = new LookaheadTrainer(cube);

    // Set up color toggle event
    document.getElementById('toggleColors').addEventListener('change', () => {
      cube.toggleColors();
    });

    // Main animation loop
    function animate() {
      requestAnimationFrame(animate);
      cube.updateAnimation();
      renderer.render();
    }

    // Start the application
    animate();

    // Expose global functions for HTML buttons
    window.cube = cube;
    window.trainer = trainer;
  </script>
</body>
</html>
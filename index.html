<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lookahead Trainer</title>
  <style>
    body { 
      margin: 0; 
      overflow: hidden; 
      font-family: sans-serif; 
    }
    
    canvas { 
      display: block; 
    }
    
    .overlay {
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(255, 255, 255, 0.9);
      padding: 10px;
      border-radius: 5px;
      z-index: 10;
    }
    
    .overlay button { 
      margin: 2px; 
    }
    
    #scrambleLength {
      width: 3em;
    }
  </style>
</head>
<body>
  <!-- Control Panel -->
  <div class="overlay">
    <label>
      <input type="checkbox" id="toggleColors" checked> Show colors
    </label><br>
    
    <button onclick="cube.reset()">Reset</button>
    
    <input type="number" id="scrambleLength" value="4" min="1" max="100">
    <button onclick="trainer.startRound()">Start Round</button>
    
    <div id="status">Click a cubelet to select it</div>
    <div><strong>Scramble:</strong> <span id="scrambleDisplay"></span></div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script type="importmap">
  {
    "imports": {
      "three": "./three/build/three.module.js",
      "three/addons/": "./three/examples/jsm/"
    }
  }
  </script>

  <script type="module">
    import * as THREE from 'three';
    import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
    import { FontLoader } from 'three/addons/loaders/FontLoader.js';
    import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

    // ======================
    // CONSTANTS & CONFIG
    // ======================
    
    const CONFIG = {
      CUBE_SPACING: 1.05,
      STICKER_OFFSET: 0.501,
      STICKER_SIZE: 0.9,
      ANIMATION_STEPS: 15,
      PULSE_SPEED: 0.005
    };

    const FACE_COLORS = {
      X: { "1": 0xff0000, "-1": 0xff8000 },  // Red/Orange
      Y: { "1": 0xffffff, "-1": 0xffff00 },  // White/Yellow
      Z: { "1": 0x00ff00, "-1": 0x0000ff }   // Green/Blue
    };

    const MOVE_MAPPING = {
      U: ['Y', 1], D: ['Y', -1],
      R: ['X', 1], L: ['X', -1], 
      F: ['Z', 1], B: ['Z', -1]
    };

    const FACE_LABELS = {
      U: { axis: 'Y', value: 1, label: 'U' },
      D: { axis: 'Y', value: -1, label: 'D' },
      F: { axis: 'Z', value: 1, label: 'F' },
      B: { axis: 'Z', value: -1, label: 'B' },
      L: { axis: 'X', value: -1, label: 'L' },
      R: { axis: 'X', value: 1, label: 'R' }
    };

    // ======================
    // SCENE SETUP
    // ======================
    
    class CubeRenderer {
      constructor() {
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.controls = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        this.init();
      }

      init() {
        // Camera setup
        this.camera.position.set(2.6, 2.6, 6.4);
        this.camera.lookAt(this.scene.position);

        // Renderer setup
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(this.renderer.domElement);

        // Controls setup
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.1;
        this.controls.rotateSpeed = 0.5;
        this.controls.zoomSpeed = 0.5;

        // Lighting setup
        this.setupLighting();
        
        // Event listeners
        this.setupEventListeners();
      }

      setupLighting() {
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 7.5);
        this.scene.add(directionalLight);

        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        this.scene.add(ambientLight);
      }

      setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('click', (event) => this.onClick(event));
      }

      onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
      }

      onClick(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);

        if (intersects.length > 0) {
          cube.handleCubeletClick(intersects[0]);
        }
      }

      render() {
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
      }
    }

    // ======================
    // CUBE LOGIC
    // ======================
    
    class RubiksCube {
      constructor(renderer) {
        this.renderer = renderer;
        this.cubelets = [];
        this.selectedCubelet = null;
        this.trackedCubelet = null;
        this.animating = false;
        
        this.createCube();
        this.addFaceLabels();
      }

      createCube() {
        // Create all 26 pieces (excluding center)
        for (let x = -1; x <= 1; x++) {
          for (let y = -1; y <= 1; y++) {
            for (let z = -1; z <= 1; z++) {
              if (x === 0 && y === 0 && z === 0) continue; // Skip center
              
              this.createCubelet(x, y, z);
            }
          }
        }
      }

      createCubelet(x, y, z) {
        // Create base cubelet
        const cubelet = new THREE.Mesh(
          new THREE.BoxGeometry(1, 1, 1),
          new THREE.MeshLambertMaterial({ color: 0x222222 })
        );
        
        cubelet.position.set(x * CONFIG.CUBE_SPACING, y * CONFIG.CUBE_SPACING, z * CONFIG.CUBE_SPACING);
        cubelet.userData = {
          position: [x, y, z],
          orientation: 0
        };

        // Add stickers to visible faces
        if (x !== 0) this.addSticker(cubelet, 'X', x);
        if (y !== 0) this.addSticker(cubelet, 'Y', y);
        if (z !== 0) this.addSticker(cubelet, 'Z', z);

        this.renderer.scene.add(cubelet);
        this.cubelets.push(cubelet);
      }

      addSticker(parent, axis, direction) {
        const showColors = document.getElementById('toggleColors')?.checked ?? true;
        const color = showColors ? FACE_COLORS[axis][direction.toString()] : 0xcccccc;
        
        const material = new THREE.MeshBasicMaterial({
          color,
          side: THREE.DoubleSide,
          depthTest: true,
          depthWrite: true
        });
        material.userData = { baseColor: color };

        const geometry = new THREE.PlaneGeometry(CONFIG.STICKER_SIZE, CONFIG.STICKER_SIZE);
        const sticker = new THREE.Mesh(geometry, material);

        // Position sticker on correct face
        this.positionSticker(sticker, axis, direction);
        parent.add(sticker);
      }

      positionSticker(sticker, axis, direction) {
        const offset = CONFIG.STICKER_OFFSET;
        
        switch (axis) {
          case 'X':
            sticker.rotation.y = Math.PI / 2;
            sticker.position.x = direction * offset;
            break;
          case 'Y':
            sticker.rotation.x = -Math.PI / 2;
            sticker.position.y = direction * offset;
            break;
          case 'Z':
            sticker.position.z = direction * offset;
            break;
        }
      }

      addFaceLabels() {
        for (const key in FACE_LABELS) {
          const { axis, value, label } = FACE_LABELS[key];
          const sprite = this.createFaceLabel(label);
          
          sprite.position[axis.toLowerCase()] = value * 1.75;
          
          // Try to attach to center piece, otherwise add to scene
          const center = this.cubelets.find(c => 
            c.userData.position[this.axisToIndex(axis)] === value && 
            c.userData.position.filter(v => v === 0).length === 2
          );
          
          if (center) {
            center.add(sprite);
          } else {
            this.renderer.scene.add(sprite);
          }
        }
      }

      createFaceLabel(text) {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 128;
        const ctx = canvas.getContext('2d');
        
        ctx.font = 'bold 64px sans-serif';
        ctx.fillStyle = 'black';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 4;
        ctx.strokeText(text, 64, 64);
        ctx.fillText(text, 64, 64);

        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;
        
        const material = new THREE.SpriteMaterial({
          map: texture,
          depthTest: true,
          depthWrite: false
        });
        
        const sprite = new THREE.Sprite(material);
        sprite.renderOrder = 999;
        sprite.scale.set(0.9, 0.9, 1);
        
        return sprite;
      }

      // ======================
      // SELECTION & INTERACTION
      // ======================

      handleCubeletClick(intersect) {
        const intersected = intersect.object;
        const parentCubelet = intersected.parent.type === 'Mesh' ? intersected.parent : intersected;

        // Skip invalid selections
        if (!this.isValidCubelet(parentCubelet)) return;

        // Handle training mode clicks - now check for sticker clicks
        if (trainer.awaitingUserGuess && this.selectedCubelet) {
          // User is guessing - check if they clicked a sticker
          if (intersected.type === 'Mesh' && intersected.geometry.type === 'PlaneGeometry') {
            trainer.handleStickerGuess(parentCubelet, intersected);
            return;
          }
        }

        // Update selection (only when not in guessing mode)
        if (!trainer.awaitingUserGuess) {
          this.updateSelection(parentCubelet, intersected);
        }
      }

      isValidCubelet(cubelet) {
        if (!cubelet.userData || !cubelet.userData.position) return false;
        
        // Skip center pieces (only 1 axis non-zero)
        const nonZeroAxes = cubelet.userData.position.filter(v => v !== 0);
        return nonZeroAxes.length > 1;
      }

      updateSelection(cubelet, sticker) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.selectedCubelet = cubelet;
        this.trackedCubelet = cubelet;
        
        // Store both sticker reference and which face it represents
        sticker.userData.originalColor = sticker.material.userData?.baseColor ?? sticker.material.color.getHex();
        cubelet.userData.originalSticker = sticker;
        
        // Store which face this sticker is on for tracking
        cubelet.userData.selectedStickerFace = this.getStickerFaceInfo(sticker);
      }

      getStickerFaceInfo(sticker) {
        // Determine which face this sticker is on
        const axis = this.getStickerAxis(sticker);
        const direction = Math.sign(sticker.position[axis.toLowerCase()]);
        return { axis, direction };
      }

      clearSelection() {
        if (!this.selectedCubelet) return;

        // Reset cubelet appearance
        this.selectedCubelet.material.color.setHex(0x222222);
        this.selectedCubelet.material.emissive.setHex(0x000000);
        this.selectedCubelet.material.emissiveIntensity = 0;
        this.selectedCubelet.material.needsUpdate = true;

        // Reset sticker appearance
        const sticker = this.selectedCubelet.userData.originalSticker;
        if (sticker && sticker.material && sticker.userData.originalColor !== undefined) {
          sticker.material.color.setHex(sticker.userData.originalColor);
        }

        // Clean up references
        if (this.selectedCubelet.userData) {
          this.selectedCubelet.userData.originalSticker = null;
        }
      }

      // ======================
      // CUBE MOVES & ANIMATION
      // ======================

      applyMoves(moves) {
        if (this.animating) return;
        
        const sequence = typeof moves === 'string' ? moves.split(' ') : moves;
        let index = 0;

        const executeNextMove = () => {
          if (index >= sequence.length) {
            this.animating = false;
            this.highlightTrackedPiece();
            return;
          }
          
          const move = sequence[index++];
          this.executeMove(move, executeNextMove);
        };

        this.animating = true;
        executeNextMove();
      }

      executeMove(move, callback) {
        const times = move.endsWith('2') ? 2 : 1;
        const baseMove = move.replace('2', '');
        let count = 0;

        const repeatMove = () => {
          if (count++ < times) {
            this.rotateLayer(baseMove, repeatMove);
          } else {
            callback();
          }
        };
        
        repeatMove();
      }

      rotateLayer(move, callback) {
        const base = move[0];
        const prime = move.includes("'");
        const [axis, layerIndex] = MOVE_MAPPING[base];

        // Get cubelets in this layer
        const threshold = 0.01;
        const layer = this.cubelets.filter(c => 
          Math.abs(c.userData.position[this.axisToIndex(axis)] - layerIndex) < threshold
        );

        // Create rotation group
        const group = new THREE.Group();
        layer.forEach(c => group.add(c));
        this.renderer.scene.add(group);

        // Calculate rotation
        const invertedFaces = ['D', 'L', 'B'];
        const isInverted = invertedFaces.includes(base);
        const angle = (Math.PI / 2) * (prime ? 1 : -1) * (isInverted ? -1 : 1);
        const rotAxis = new THREE.Vector3(
          axis === 'X' ? 1 : 0,
          axis === 'Y' ? 1 : 0,
          axis === 'Z' ? 1 : 0
        );

        this.animateRotation(group, layer, rotAxis, angle, callback);
      }

      animateRotation(group, layer, rotAxis, totalAngle, callback) {
        let currentStep = 0;
        const stepAngle = totalAngle / CONFIG.ANIMATION_STEPS;

        const animateStep = () => {
          if (currentStep++ < CONFIG.ANIMATION_STEPS) {
            group.rotateOnAxis(rotAxis, stepAngle);
            requestAnimationFrame(animateStep);
          } else {
            this.finishRotation(group, layer);
            if (callback) callback();
          }
        };
        
        animateStep();
      }

      finishRotation(group, layer) {
        group.updateMatrixWorld(true);
        
        layer.forEach(cubelet => {
          // Update orientation for edge pieces
          this.updateCubeletOrientation(cubelet, group);
          
          // Apply transformation and update position
          cubelet.applyMatrix4(group.matrixWorld);
          cubelet.matrixAutoUpdate = true;
          this.renderer.scene.add(cubelet);

          // Update logical position
          cubelet.userData.position = [
            Math.round(cubelet.position.x / CONFIG.CUBE_SPACING),
            Math.round(cubelet.position.y / CONFIG.CUBE_SPACING),
            Math.round(cubelet.position.z / CONFIG.CUBE_SPACING)
          ];
        });
        
        this.renderer.scene.remove(group);
      }

      updateCubeletOrientation(cubelet, group) {
        // Determine if this is an edge piece and should flip orientation
        const pos = cubelet.userData.position;
        const nonZeroAxes = pos.map((v, i) => v !== 0 ? i : -1).filter(i => i !== -1);
        const isEdge = nonZeroAxes.length === 2;
        
        // Get rotation axis from group's rotation
        // This is a simplified version - in practice you'd track the rotation axis
        // For now, we'll use a heuristic based on the move being performed
        if (isEdge) {
          // Flip orientation for edge pieces in certain rotations
          // This is a simplified implementation
          cubelet.userData.orientation ^= 1;
        }
      }

      highlightTrackedPiece() {
        if (this.selectedCubelet) {
          this.selectedCubelet.material.color.set(0x00ff00);
        }
      }

      // ======================
      // COLOR MANAGEMENT
      // ======================

      toggleColors() {
        const showColors = document.getElementById('toggleColors').checked;
        
        this.cubelets.forEach(cubelet => {
          cubelet.children.forEach(sticker => {
            if (!sticker.material) return;
            
            const axis = this.getStickerAxis(sticker);
            const direction = Math.sign(sticker.position[axis.toLowerCase()]);
            const color = showColors ? FACE_COLORS[axis][direction.toString()] : 0xcccccc;
            
            this.animateColorTransition(sticker, color);
          });
        });
      }

      getStickerAxis(sticker) {
        if (Math.abs(sticker.position.x) > 0.5) return 'X';
        if (Math.abs(sticker.position.y) > 0.5) return 'Y';
        return 'Z';
      }

      animateColorTransition(sticker, targetColor) {
        const currentColor = sticker.material.color.clone();
        const newColor = new THREE.Color(targetColor);
        let progress = 0;
        const duration = 300;
        const steps = 30;
        const stepTime = duration / steps;

        const animate = () => {
          progress += 1 / steps;
          sticker.material.color.lerpColors(currentColor, newColor, progress);
          if (progress < 1) {
            setTimeout(animate, stepTime);
          }
        };
        
        animate();
      }

      // ======================
      // UTILITY METHODS
      // ======================

      axisToIndex(axis) {
        return { X: 0, Y: 1, Z: 2 }[axis];
      }

      snapToAxis(value) {
        const epsilon = 0.1;
        if (Math.abs(value - CONFIG.CUBE_SPACING) < epsilon) return 1;
        if (Math.abs(value + CONFIG.CUBE_SPACING) < epsilon) return -1;
        if (Math.abs(value) < epsilon) return 0;
        return NaN;
      }

      reset(clearSelection = true) {
        if (clearSelection) {
          this.clearSelection();
          this.selectedCubelet = null;
          this.trackedCubelet = null;
        }

        // Reset all cubelets to default state
        this.cubelets.forEach(c => {
          c.material.color.set(0x222222);
          c.userData.orientation = 0;
        });

        // Reset positions (this is a simplified reset)
        this.applyMoves([]); // Empty move sequence
      }

      // Animation update for selected piece pulsing
      updateAnimation() {
        if (this.selectedCubelet && this.selectedCubelet.userData.originalSticker) {
          const t = Date.now() * CONFIG.PULSE_SPEED;
          const blend = 0.5 + 0.5 * Math.sin(t);
          
          const sticker = this.selectedCubelet.userData.originalSticker;
          const baseColor = new THREE.Color(sticker.userData.originalColor);
          const targetColor = new THREE.Color(0xba55d3); // Purple highlight
          sticker.material.color.copy(baseColor).lerp(targetColor, blend);
        }

        if (this.selectedCubelet) {
          const t = Date.now() * CONFIG.PULSE_SPEED;
          const intensity = 0.5 + 0.25 * Math.sin(t);
          this.selectedCubelet.material.emissive = new THREE.Color(0xba55d3);
          this.selectedCubelet.material.emissiveIntensity = intensity;
          this.selectedCubelet.material.needsUpdate = true;
        }
      }
    }

    // ======================
    // TRAINING SYSTEM
    // ======================
    
    class LookaheadTrainer {
      constructor(cube) {
        this.cube = cube;
        this.currentScramble = [];
        this.awaitingUserGuess = false;
        this.originalCubelet = null;
        this.originalSticker = null;
        this.userGuess = null;
      }

      startRound() {
        this.cube.reset(false); // Don't clear selection
        
        // Store references to the original cubelet and sticker
        if (this.cube.selectedCubelet && this.cube.selectedCubelet.userData.originalSticker) {
          this.originalCubelet = this.cube.selectedCubelet;
          this.originalSticker = this.cube.selectedCubelet.userData.originalSticker;
        } else {
          document.getElementById('status').innerText = 'Please select a piece first!';
          return;
        }
        
        const length = parseInt(document.getElementById('scrambleLength').value);
        this.currentScramble = this.generateScramble(length);
        
        document.getElementById('scrambleDisplay').innerText = this.currentScramble.join(' ');
        document.getElementById('status').innerText = 'Now click the sticker where you think the selected sticker will end up';
        
        this.awaitingUserGuess = true;
      }

      generateScramble(length = 10) {
        const faces = ['U', 'D', 'R', 'L', 'F', 'B'];
        const modifiers = ['', "'", '2'];
        const scramble = [];

        while (scramble.length < length) {
          const face = faces[Math.floor(Math.random() * faces.length)];
          const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
          const move = face + modifier;

          // Avoid immediate cancellation or repetition
          const lastMove = scramble[scramble.length - 1];
          if (!lastMove || !this.movesConflict(lastMove, move)) {
            scramble.push(move);
          }
        }

        return scramble;
      }

      movesConflict(move1, move2) {
        // Simple conflict detection - same face
        return move1[0] === move2[0];
      }

      handleGuess(guessedCubelet) {
        // Ask for orientation guess
        Swal.fire({
          title: 'What is the orientation of the piece?',
          input: 'radio',
          inputOptions: {
            0: '0° (correct)',
            1: 'Flipped (1)'
          },
          customClass: {
            popup: 'swal2-bottom-left'
          },
          toast: true,
          position: 'bottom-start',
          showConfirmButton: true,
          confirmButtonText: 'Submit Guess'
        }).then(result => {
          if (!result.isConfirmed) return;

          const guessedOrientation = parseInt(result.value);
          this.checkGuess(guessedCubelet, guessedOrientation);
        });
      }

      handleStickerGuess(guessedCubelet, guessedSticker) {
        // Store the user's guess
        this.userGuess = {
          cubelet: guessedCubelet,
          sticker: guessedSticker
        };
        
        // Create a clone of the cube to apply the scramble
        // This way we can compare the user's guess with the actual result
        this.checkGuessWithClonedCube();
      }

      checkGuessWithClonedCube() {
        // We'll use a different approach - apply the scramble and check the result
        // without modifying the original cube state
        
        // First, mark the original cubelet and sticker for tracking
        this.originalCubelet.userData._isTracked = true;
        this.originalSticker.userData._isTrackedSticker = true;
        
        // Apply the scramble
        this.cube.applyMoves(this.currentScramble);
        
        // Set up a check to run after the animation completes
        const checkComplete = setInterval(() => {
          if (!this.cube.animating) {
            clearInterval(checkComplete);
            this.evaluateGuessAfterScramble();
          }
        }, 50);
      }

      evaluateGuessAfterScramble() {
        // Find the tracked cubelet after scramble
        const trackedCubelet = this.cube.cubelets.find(c => c.userData._isTracked);
        
        // Find the tracked sticker after scramble
        let trackedSticker = null;
        if (trackedCubelet) {
          trackedSticker = trackedCubelet.children.find(c => c.userData._isTrackedSticker);
        }
        
        // Clean up tracking flags
        this.originalCubelet.userData._isTracked = false;
        this.originalSticker.userData._isTrackedSticker = false;
        
        // Check if the user's guess matches the actual result
        const locationCorrect = this.userGuess.cubelet === trackedCubelet;
        
        // For sticker correctness, we need to check if they're the same sticker
        // This is tricky, but we can use the fact that the sticker should be the same object
        const stickerCorrect = this.userGuess.sticker === trackedSticker;
        
        // Provide feedback
        let message;
        let isSuccess = false;
        
        if (locationCorrect && stickerCorrect) {
          message = '✅ Perfect! Your prediction was correct!';
          isSuccess = true;
        } else if (locationCorrect && !stickerCorrect) {
          message = '🟡 You found the right piece, but clicked on the wrong sticker face.';
        } else {
          message = '❌ Incorrect piece location.';
        }
        
        // Highlight the correct answer
        if (trackedSticker) {
          this.highlightCorrectAnswer(trackedSticker);
        }
        
        document.getElementById('status').innerText = message;
        
        if (isSuccess) {
          setTimeout(() => {
            this.cube.reset();
            this.startRound();
          }, 2000);
        } else {
          setTimeout(() => {
            document.getElementById('status').innerText = 'Try again or hit Reset for a new round.';
          }, 3000);
        }
        
        this.awaitingUserGuess = false;
      }

      highlightCorrectAnswer(sticker) {
        // Briefly highlight the correct sticker
        const originalColor = sticker.material.color.getHex();
        sticker.material.color.setHex(0x00ff00); // Green
        
        setTimeout(() => {
          sticker.material.color.setHex(originalColor);
        }, 2000);
      }

      reset() {
        this.awaitingUserGuess = false;
        this.currentScramble = [];
        this.originalCubelet = null;
        this.originalSticker = null;
        this.userGuess = null;
        document.getElementById('status').innerText = 'Select a piece, then click Start Round to begin tracking';
        document.getElementById('scrambleDisplay').innerText = '';
        this.cube.reset();
      }
    }

    // ======================
    // MAIN APPLICATION
    // ======================
    
    // Initialize the application
    const renderer = new CubeRenderer();
    const cube = new RubiksCube(renderer);
    const trainer = new LookaheadTrainer(cube);

    // Set up color toggle event
    document.getElementById('toggleColors').addEventListener('change', () => {
      cube.toggleColors();
    });

    // Main animation loop
    function animate() {
      requestAnimationFrame(animate);
      cube.updateAnimation();
      renderer.render();
    }

    // Start the application
    animate();

    // Expose global functions for HTML buttons
    window.cube = cube;
    window.trainer = trainer;
  </script>
</body>
</html>
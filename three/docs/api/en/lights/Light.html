<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		[page:Object3D] &rarr;

		<h1>[name]</h1>

		<p class="desc">
			Abstract base class for lights - all other light types inherit the
			properties and methods described here.
		</p>

		<h2>Constructor</h2>

		<h3>[name]( [param:Integer color], [param:Float intensity] )</h3>
		<p>
			[page:Integer color] - (optional) hexadecimal color of the light. Default
			is 0xffffff (white).<br />
			[page:Float intensity] - (optional) numeric value of the light's
			strength/intensity. Default is `1`.<br /><br />
			Creates a new [name]. Note that this is not intended to be called directly
			(use one of derived classes instead).
		</p>

		<h2>Properties</h2>
		<p>See the base [page:Object3D Object3D] class for common properties.</p>

		<h3>[property:Color color]</h3>
		<p>
			Color of the light. Defaults to a new [page:Color] set to white, if not
			passed in the constructor.<br />
		</p>

		<h3>[property:Float intensity]</h3>
		<p>
			The light's intensity, or strength.<br />
			The units of intensity depend on the type of light.<br />
			Default - `1.0`.
		</p>

		<h3>[property:Boolean isLight]</h3>
		<p>Read-only flag to check if a given object is of type [name].</p>

		<h2>Methods</h2>
		<p>See the base [page:Object3D Object3D] class for common methods.</p>

		<h3>[method:undefined dispose]()</h3>
		<p>
			Abstract dispose method for classes that extend this class; implemented by
			subclasses that have disposable GPU-related resources.
		</p>

		<h3>[method:this copy]( [param:Light source] )</h3>
		<p>
			Copies the value of [page:.color color] and [page:.intensity intensity]
			from the [page:Light source] light into this one.
		</p>

		<h3>[method:Object toJSON]( [param:Object meta] )</h3>
		<p>
			meta -- object containing metadata such as materials, textures for
			objects.<br />
			Convert the light to three.js
			[link:https://github.com/mrdoob/three.js/wiki/JSON-Object-Scene-format-4 JSON Object/Scene format].
		</p>

		<h2>Source</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>

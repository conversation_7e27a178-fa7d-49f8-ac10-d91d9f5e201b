<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		[page:Object3D] &rarr; [page:Light] &rarr;

		<h1>[name]</h1>

		<p class="desc">
			Light probes are an alternative way of adding light to a 3D scene. Unlike
			classical light sources (e.g. directional, point or spot lights), light
			probes do not emit light. Instead they store information about light
			passing through 3D space. During rendering, the light that hits a 3D
			object is approximated by using the data from the light probe.
		</p>

		<p class="desc">
			Light probes are usually created from (radiance) environment maps. The
			class [page:LightProbeGenerator] can be used to create light probes from
			instances of [page:CubeTexture] or [page:WebGLCubeRenderTarget]. However,
			light estimation data could also be provided in other forms e.g. by WebXR.
			This enables the rendering of augmented reality content that reacts to
			real world lighting.
		</p>

		<p class="desc">
			The current probe implementation in three.js supports so-called diffuse
			light probes. This type of light probe is functionally equivalent to an
			irradiance environment map.
		</p>

		<h2>Examples</h2>
		<p>
			[example:webgl_lightprobe WebGL / light probe ]<br />
			[example:webgl_lightprobe_cubecamera WebGL / light probe / cube camera ]
		</p>

		<h2>Constructor</h2>

		<h3>[name]( [param:SphericalHarmonics3 sh], [param:Float intensity] )</h3>
		<p>
			[page:SphericalHarmonics3 sh] - (optional) An instance of
			[page:SphericalHarmonics3].<br />
			[page:Float intensity] - (optional) Numeric value of the light probe's
			intensity. Default is `1`.<br /><br />

			Creates a new [name].
		</p>

		<h2>Properties</h2>
		<p>
			See the base [page:Light Light] class for common properties. The
			[page:Light.color color] property is currently not evaluated and thus has
			no effect.
		</p>

		<h3>[property:Boolean isLightProbe]</h3>
		<p>Read-only flag to check if a given object is of type [name].</p>

		<h3>[property:SphericalHarmonics3 sh]</h3>
		<p>
			A light probe uses spherical harmonics to encode lighting information.
		</p>

		<h2>Methods</h2>
		<p>See the base [page:Light Light] class for common methods.</p>
		<h2>Source</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>

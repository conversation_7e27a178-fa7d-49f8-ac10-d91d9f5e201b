<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		[page:Object3D] &rarr; [page:Light] &rarr;

		<h1>[name]</h1>

		<p class="desc">
			This light gets emitted from a single point in one direction, along a cone
			that increases in size the further from the light it gets. <br /><br />
			This light can cast shadows - see the [page:SpotLightShadow] page for
			details.
		</p>

		<h2>Code Example</h2>
		<code>
		// white spotlight shining from the side, modulated by a texture, casting a shadow

		const spotLight = new THREE.SpotLight( 0xffffff );
		spotLight.position.set( 100, 1000, 100 );
		spotLight.map = new THREE.TextureLoader().load( url );

		spotLight.castShadow = true;

		spotLight.shadow.mapSize.width = 1024;
		spotLight.shadow.mapSize.height = 1024;

		spotLight.shadow.camera.near = 500;
		spotLight.shadow.camera.far = 4000;
		spotLight.shadow.camera.fov = 30;

		scene.add( spotLight );
		</code>

		<h2>Examples</h2>
		<p>
			[example:webgl_lights_spotlight lights / spotlight ]<br />
			[example:webgl_lights_spotlights lights / spotlights ]
		</p>

		<h2>Constructor</h2>
		<h3>
			[name]( [param:Integer color], [param:Float intensity], [param:Float distance], [param:Radians angle], [param:Float penumbra], [param:Float decay] )
		</h3>
		<p>
			[page:Integer color] - (optional) hexadecimal color of the light. Default
			is 0xffffff (white).<br />
			[page:Float intensity] - (optional) numeric value of the light's
			strength/intensity. Default is `1`.<br />
			[page:Float distance] - Maximum range of the light. Default is `0` (no
			limit).<br />
			[page:Radians angle] - Maximum angle of light dispersion from its
			direction whose upper bound is Math.PI/2.<br />
			[page:Float penumbra] - Percent of the spotlight cone that is attenuated
			due to penumbra. Takes values between zero and `1`. Default is zero.<br />
			[page:Float decay] - The amount the light dims along the distance of the
			light.<br /><br />
			Creates a new [name].
		</p>

		<h2>Properties</h2>
		<p>See the base [page:Light Light] class for common properties.</p>

		<h3>[property:Float angle]</h3>
		<p>
			Maximum extent of the spotlight, in radians, from its direction. Should be
			no more than `Math.PI/2`. The default is `Math.PI/3`.
		</p>

		<h3>[property:Boolean castShadow]</h3>
		<p>
			If set to `true` light will cast dynamic shadows. *Warning*: This is
			expensive and requires tweaking to get shadows looking right. See the
			[page:SpotLightShadow] for details. The default is `false`.
		</p>

		<h3>[property:Float decay]</h3>
		<p>
			The amount the light dims along the distance of the light. Default is
			`2`.<br />
			In context of physically-correct rendering the default value should not be
			changed.
		</p>

		<h3>[property:Float distance]</h3>
		<p>
			When distance is zero, light will attenuate according to inverse-square
			law to infinite distance. When distance is non-zero, light will attenuate
			according to inverse-square law until near the distance cutoff, where it
			will then attenuate quickly and smoothly to `0`. Inherently, cutoffs are
			not physically correct.
		</p>
		<p>Default is `0.0`.</p>

		<h3>[property:Float intensity]</h3>
		<p>
			The light's luminous intensity measured in candela (cd). Default is `1`.
			<br /><br />
			Changing the intensity will also change the light's power.
		</p>

		<h3>[property:Boolean isSpotLight]</h3>
		<p>Read-only flag to check if a given object is of type [name].</p>

		<h3>[property:Float penumbra]</h3>
		<p>
			Percent of the spotlight cone that is attenuated due to penumbra. Takes
			values between zero and `1`. The default is `0.0`.
		</p>

		<h3>[property:Vector3 position]</h3>
		<p>
			This is set equal to [page:Object3D.DEFAULT_UP] (0, 1, 0), so that the
			light shines from the top down.
		</p>

		<h3>[property:Float power]</h3>
		<p>
			The light's power.<br />
			Power is the luminous power of the light measured in lumens (lm).
			<br /><br />
			Changing the power will also change the light's intensity.
		</p>

		<h3>[property:SpotLightShadow shadow]</h3>
		<p>A [page:SpotLightShadow] used to calculate shadows for this light.</p>

		<h3>[property:Object3D target]</h3>
		<p>
			The Spotlight points from its [page:.position position] to
			target.position. The default position of the target is `(0, 0, 0)`.<br />
			*Note*: For the target's position to be changed to anything other than the
			default, it must be added to the [page:Scene scene] using
			<code> scene.add( light.target ); </code>
			This is so that the target's [page:Object3D.matrixWorld matrixWorld] gets
			automatically updated each frame.<br /><br />
			It is also possible to set the target to be another object in the scene
			(anything with a [page:Object3D.position position] property), like so:
			<code>
const targetObject = new THREE.Object3D(); 
scene.add(targetObject);

light.target = targetObject;
			</code>
			The spotlight will now track the target object.
		</p>

		<h3>[property:Texture map]</h3>
		<p>
			A [page:Texture] used to modulate the color of the light. The spot light
			color is mixed with the RGB value of this texture, with a ratio
			corresponding to its alpha value. The cookie-like masking effect is
			reproduced using pixel values (0, 0, 0, 1-cookie_value). *Warning*:
			[page:.map] is disabled if [page:.castShadow] is *false*.
		</p>

		<h2>Methods</h2>

		<p>See the base [page:Light Light] class for common methods.</p>

		<h3>[method:undefined dispose]()</h3>
		<p>
			Frees the GPU-related resources allocated by this instance. Call this
			method whenever this instance is no longer used in your app.
		</p>

		<h3>[method:this copy]( [param:SpotLight source] )</h3>
		<p>
			Copies value of all the properties from the [page:SpotLight source] to
			this SpotLight.
		</p>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>

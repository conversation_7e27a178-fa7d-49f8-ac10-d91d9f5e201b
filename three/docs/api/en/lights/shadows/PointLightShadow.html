<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		[page:LightShadow] &rarr;

		<h1>[name]</h1>

		<p class="desc">
			This is used internally by [page:PointLight PointLights] for calculating
			shadows.
		</p>

		<h2>Code Example</h2>
		<code>
		//Create a WebGLRenderer and turn on shadows in the renderer
		const renderer = new THREE.WebGLRenderer();
		renderer.shadowMap.enabled = true;
		renderer.shadowMap.type = THREE.PCFSoftShadowMap; // default THREE.PCFShadowMap

		//Create a PointLight and turn on shadows for the light
		const light = new THREE.PointLight( 0xffffff, 1, 100 );
		light.position.set( 0, 10, 4 );
		light.castShadow = true; // default false
		scene.add( light );

		//Set up shadow properties for the light
		light.shadow.mapSize.width = 512; // default
		light.shadow.mapSize.height = 512; // default
		light.shadow.camera.near = 0.5; // default
		light.shadow.camera.far = 500; // default

		//Create a sphere that cast shadows (but does not receive them)
		const sphereGeometry = new THREE.SphereGeometry( 5, 32, 32 );
		const sphereMaterial = new THREE.MeshStandardMaterial( { color: 0xff0000 } );
		const sphere = new THREE.Mesh( sphereGeometry, sphereMaterial );
		sphere.castShadow = true; //default is false
		sphere.receiveShadow = false; //default
		scene.add( sphere );

		//Create a plane that receives shadows (but does not cast them)
		const planeGeometry = new THREE.PlaneGeometry( 20, 20, 32, 32 );
		const planeMaterial = new THREE.MeshStandardMaterial( { color: 0x00ff00 } )
		const plane = new THREE.Mesh( planeGeometry, planeMaterial );
		plane.receiveShadow = true;
		scene.add( plane );

		//Create a helper for the shadow camera (optional)
		const helper = new THREE.CameraHelper( light.shadow.camera );
		scene.add( helper );
		</code>

		<h2>Constructor</h2>
		<h3>[name]( )</h3>
		<p>
			Creates a new [name]. This is not intended to be called directly - it is
			called internally by [page:PointLight].
		</p>

		<h2>Properties</h2>
		<p>
			See the base [page:LightShadow LightShadow] class for common properties.
		</p>

		<h3>[property:Boolean isPointLightShadow]</h3>
		<p>Read-only flag to check if a given object is of type [name].</p>

		<h2>Methods</h2>

		<p>See the base [page:LightShadow LightShadow] class for common methods.</p>

		<h3>
			[method:undefined updateMatrices]( [param:Light light], [param:number viewportIndex])
		</h3>
		<p>
			Update the matrices for the camera and shadow, used internally by the
			renderer.<br /><br />

			light -- the light for which the shadow is being rendered.<br />
			viewportIndex -- calculates the matrix for this viewport
		</p>

		<h2>Source</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/lights/[name].js src/lights/[name].js]
		</p>
	</body>
</html>

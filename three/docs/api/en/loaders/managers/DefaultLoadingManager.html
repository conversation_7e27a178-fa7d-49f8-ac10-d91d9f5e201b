<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">
			A global instance of the [page:LoadingManager LoadingManager], used by
			most loaders when no custom manager has been specified.<br /><br />

			This will be sufficient for most purposes, however there may be times when
			you desire separate loading managers for say, textures and models.
		</p>

		<h2>Code Example</h2>

		<p>
			You can optionally set the [page:LoadingManager.onStart onStart],
			[page:LoadingManager.onLoad onLoad], [page:LoadingManager.onProgress onProgress], 
			[page:LoadingManager.onStart onError] functions for the
			manager. These will then apply to any loaders using the
			DefaultLoadingManager.<br /><br />

			Note that these shouldn't be confused with the similarly named functions
			of individual loaders, as they are intended for displaying information
			about the overall status of loading, rather than dealing with the data
			that has been loaded.
		</p>
		<code>
THREE.DefaultLoadingManager.onStart = function ( url, itemsLoaded, itemsTotal ) {
	console.log( 'Started loading file: ' + url + '.\nLoaded ' + itemsLoaded + ' of ' + itemsTotal + ' files.' );
};

THREE.DefaultLoadingManager.onLoad = function ( ) {
	console.log( 'Loading Complete!');
};

THREE.DefaultLoadingManager.onProgress = function ( url, itemsLoaded, itemsTotal ) {
	console.log( 'Loading file: ' + url + '.\nLoaded ' + itemsLoaded + ' of ' + itemsTotal + ' files.' );
};

THREE.DefaultLoadingManager.onError = function ( url ) {
	console.log( 'There was an error loading ' + url );
};
		</code>

		<h2>Properties</h2>
		<p>
			See the [page:LoadingManager LoadingManager] page for details of
			properties.
		</p>

		<h2>Methods</h2>
		<p>
			See the [page:LoadingManager LoadingManager] page for details of methods.
		</p>

		<h2>Source</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/loaders/LoadingManager.js src/loaders/LoadingManager.js]
		</p>
	</body>
</html>
